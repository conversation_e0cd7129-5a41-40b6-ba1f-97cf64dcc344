from maix import image, display, app, time, camera
import cv2
import numpy as np

# 初始化显示器，用于显示处理后的图像
try:
    disp = display.Display()
    print("调试：显示器初始化成功")
except Exception as e:
    print(f"错误：显示器初始化失败: {e}")
    disp = None

# 初始化摄像头，设置分辨率为640x480，采集灰度图像
try:
    cam = camera.Camera(640, 480, image.Format.FMT_GRAYSCALE)
    print("调试：摄像头初始化成功")
except Exception as e:
    print(f"错误：摄像头初始化失败: {e}")
    cam = None

# 定义ROI（感兴趣区域）的参数，用于截取图像中需要处理的部分
roi_x = 228  # ROI区域左上角x坐标
roi_y = 84  # ROI区域左上角y坐标
roi_w = 200  # ROI区域宽度
roi_h = 310  # ROI区域高度

def ensure_multiple_of_two(img):
    """确保图像的宽度和高度都是2的倍数，避免后续处理可能出现的尺寸问题"""
    # 获取图像的高度和宽度
    h, w = img.shape[:2]
    # 计算调整后的宽度（若原宽度不是2的倍数，则减1）
    new_w = w if w % 2 == 0 else w - 1
    # 计算调整后的高度（若原高度不是2的倍数，则减1）
    new_h = h if h % 2 == 0 else h - 1
    # 若尺寸有调整，则 resize 图像到新尺寸
    if new_w != w or new_h != h:
        return cv2.resize(img, (new_w, new_h))
    return img

# 从初始代码引入的工具函数
def calculate_distance(point1, point2):
    """计算两点之间的欧几里得距离"""
    return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

def calculate_edge_overlap_with_polygon(edge_start, edge_end, polygon_points, overlap_threshold=0.5):
    """
    计算边与多边形的重叠程度
    返回重叠比例 (0.0 - 1.0)
    """
    try:
        if edge_start == edge_end:
            print("警告：边的起点和终点相同")
            return 0.0

        # 计算边长，如果太短则返回0
        edge_length = calculate_distance(edge_start, edge_end)
        if edge_length < 1.0:  # 边长小于1像素
            print(f"警告：边长太短 ({edge_length:.2f} pixels)")
            return 0.0

        # 简化的重叠计算：检查边的中点和几个采样点是否在多边形内
        sample_points = []
        num_samples = 5
        for i in range(num_samples):
            t = i / (num_samples - 1)
            sample_x = edge_start[0] + t * (edge_end[0] - edge_start[0])
            sample_y = edge_start[1] + t * (edge_end[1] - edge_start[1])
            sample_points.append((sample_x, sample_y))

        inside_count = 0
        for point in sample_points:
            if cv2.pointPolygonTest(polygon_points, point, False) >= 0:
                inside_count += 1

        overlap_ratio = inside_count / num_samples
        return overlap_ratio

    except Exception as e:
        print(f"计算边重叠度时出错: {e}")
        return 0.0

def extract_noise_points_from_contour(contour, edges_image):
    """
    提取轮廓内部的噪声点（白色像素点）
    """
    if edges_image is None or len(contour) < 3:
        return []

    # 获取轮廓边界框
    x, y, w, h = cv2.boundingRect(contour)
    noise_points = []

    # 在边界框内搜索噪声点
    for py in range(y, y + h):
        for px in range(x, x + w):
            # 检查点是否在轮廓内部
            if cv2.pointPolygonTest(contour, (px, py), False) > 0:  # 严格内部
                # 检查是否为噪声点（边缘检测图像中的白色像素）
                if edges_image[py, px] > 0:
                    noise_points.append((px, py))

    return noise_points

def find_closest_noise_point_from_list(isolated_point, noise_points):
    """
    从预存储的噪声点列表中找到离孤立拐点最近的噪声点
    """
    if not noise_points:
        return None

    closest_point = None
    min_distance = float('inf')

    for noise_point in noise_points:
        distance = calculate_distance(isolated_point, noise_point)
        if distance < min_distance:
            min_distance = distance
            closest_point = noise_point

    print(f"        从 {len(noise_points)} 个预存储噪声点中找到最近点: {closest_point}, 距离: {min_distance:.2f}")
    return closest_point

# 新增：处理轮廓拐点数组的函数（改为拐点分析）########################################################
def process_contour_corners(contour_corners_list, flipped_binary):
    """
    为每个轮廓创建独立的拐点数组（JD1、JD2...），使用初始代码的拐点分析方法
    """
    if not contour_corners_list or flipped_binary is None:
        return

    overlap_threshold = 0.5  # 重叠阈值
    jd_dict = {}  # 用字典存储所有轮廓的拐点数组（键为"JD1"、"JD2"...）

    for contour_idx, corners in enumerate(contour_corners_list):
        contour_id = contour_idx + 1
        n = len(corners)
        if n == 0:
            continue

        # 定义当前轮廓的拐点数组名称（JD1、JD2...）
        jd_name = f"JD{contour_id}"

        # 提取顶点坐标
        vertices = [(corner[0], corner[1]) for corner in corners]

        # 使用初始代码的拐点分析方法
        corner_marks = []

        print(f"\n{jd_name}（轮廓{contour_id}的拐点分析，{n}个顶点）：")

        # 第一步：根据边与多边形重叠度进行初始标记
        initial_marks = []
        for i in range(len(vertices)):
            current_vertex = vertices[i]
            prev_vertex = vertices[(i - 1) % len(vertices)]
            next_vertex = vertices[(i + 1) % len(vertices)]

            # 计算当前拐点对应的边（连接前一个和下一个顶点）
            edge_start = prev_vertex
            edge_end = next_vertex

            # 将顶点列表转换为多边形轮廓格式
            polygon_points = np.array(vertices, dtype=np.int32)

            # 计算该边与多边形的重叠程度
            overlap_ratio = calculate_edge_overlap_with_polygon(
                edge_start, edge_end, polygon_points, overlap_threshold
            )

            # 根据重叠程度进行初始标记
            if overlap_ratio >= overlap_threshold:
                initial_marks.append(1)
                print(f"  拐点 {i} 初始标记为 1 (重叠度: {overlap_ratio:.3f} >= {overlap_threshold})")
            else:
                initial_marks.append(0)
                print(f"  拐点 {i} 初始标记为 0 (重叠度: {overlap_ratio:.3f} < {overlap_threshold})")

        # 第二步：识别孤立拐点并进行最终标记
        corner_marks = []
        isolated_count = 0

        print(f"  识别孤立拐点:")
        for i in range(len(vertices)):
            current_mark = initial_marks[i]
            prev_mark = initial_marks[(i - 1) % len(vertices)]
            next_mark = initial_marks[(i + 1) % len(vertices)]

            # 检查是否为孤立拐点：当前为有效拐点(1)，但前后都是无效拐点(0)
            if current_mark == 1 and prev_mark == 0 and next_mark == 0:
                corner_marks.append(2)  # 标记为孤立拐点
                isolated_count += 1
                print(f"    拐点 {i} 标记为 2 (孤立拐点: 前{prev_mark}-当前{current_mark}-后{next_mark})")
            else:
                corner_marks.append(current_mark)  # 保持原始标记
                if current_mark == 1:
                    print(f"    拐点 {i} 保持标记为 1 (非孤立: 前{prev_mark}-当前{current_mark}-后{next_mark})")
                else:
                    print(f"    拐点 {i} 保持标记为 0 (无效拐点)")

        print(f"  识别结果: 发现 {isolated_count} 个孤立拐点")

        # 初始化3*n数组（X坐标、Y坐标、拐点标志）
        jd_array = np.zeros((3, n), dtype=np.float32)

        for i, (vertex, mark) in enumerate(zip(vertices, corner_marks)):
            x, y = vertex
            # 填充数组
            jd_array[0, i] = x  # X坐标行
            jd_array[1, i] = y  # Y坐标行
            jd_array[2, i] = mark  # 标志行（0=无效，1=有效，2=孤立）

        # 存储到字典（键为JD1、JD2等）
        jd_dict[jd_name] = jd_array

        # 输出当前轮廓的拐点数组
        print("X坐标行：", jd_array[0])
        print("Y坐标行：", jd_array[1])
        print("标志行（0=无效，1=有效，2=孤立）：", jd_array[2])
        print("完整数组：\n", jd_array)

    # 可选：返回所有JD数组（便于后续调用）
    return jd_dict

#############拐点数组转为边数组（使用初始代码的有效边判断）######################
def convert_jd_to_edge_array(jd_dict):
    """
    将拐点数组JDn转换为边数组（5*n）
    转换规则（基于初始代码的有效边判断）：
    - 边数组为5行n列（n为拐点数量）
    - 第1行：当前拐点X坐标
    - 第2行：当前拐点Y坐标
    - 第3行：下一拐点X坐标（最后一个拐点的下一个为第一个拐点）
    - 第4行：下一拐点Y坐标
    - 第5行：边标志位（只有当前拐点和下一拐点都标记为1时为1，否则为2）
    """
    if not jd_dict:
        print("没有可处理的拐点数组")
        return {}

    edge_arrays = {}  # 存储所有边数组，键为"Edge1"、"Edge2"...

    for jd_name, jd_array in jd_dict.items():
        # 提取轮廓ID，生成边数组名称（如JD1对应Edge1）
        contour_id = int(jd_name.replace("JD", ""))
        edge_name = f"Edge{contour_id}"

        n = jd_array.shape[1]  # 拐点数量（边数量与拐点数量相同）
        if n < 2:
            print(f"{jd_name}拐点数量不足，无法生成边数组")
            continue

        # 初始化5*n边数组
        edge_array = np.zeros((5, n), dtype=np.float32)

        # 填充边数组
        for i in range(n):
            # 当前拐点索引
            current_idx = i
            # 下一拐点索引（闭合轮廓，最后一个连接到第一个）
            next_idx = (i + 1) % n

            # 1-2行：当前拐点坐标
            edge_array[0, i] = jd_array[0, current_idx]  # 当前X
            edge_array[1, i] = jd_array[1, current_idx]  # 当前Y

            # 3-4行：下一拐点坐标
            edge_array[2, i] = jd_array[0, next_idx]     # 下一个X
            edge_array[3, i] = jd_array[1, next_idx]     # 下一个Y

            # 第5行：边标志位（基于初始代码：只有相邻拐点都标记为1时才是有效边）
            current_flag = jd_array[2, current_idx]
            next_flag = jd_array[2, next_idx]
            edge_flag = 1 if (current_flag == 1 and next_flag == 1) else 2
            edge_array[4, i] = edge_flag

        # 存储边数组
        edge_arrays[edge_name] = edge_array

        # 输出转换结果
        print(f"\n{edge_name}（轮廓{contour_id}的边数组，5*{n}）：")
        print("第1行（当前X）：", edge_array[0])
        print("第2行（当前Y）：", edge_array[1])
        print("第3行（下一个X）：", edge_array[2])
        print("第4行（下一个Y）：", edge_array[3])
        print("第5行（边标志，1=有效边）：", edge_array[4])
        print("完整边数组：\n", edge_array)

    return edge_arrays

# 从初始代码引入的孤立拐点处理函数
def point_to_line_distance(point, line_start, line_end):
    """
    计算点到直线的垂直距离
    """
    x0, y0 = point
    x1, y1 = line_start
    x2, y2 = line_end

    # 如果直线起点和终点相同，返回点到点的距离
    if x1 == x2 and y1 == y2:
        return calculate_distance(point, line_start)

    # 使用点到直线距离公式: |ax0 + by0 + c| / sqrt(a^2 + b^2)
    # 直线方程: (y2-y1)x - (x2-x1)y + (x2-x1)y1 - (y2-y1)x1 = 0
    a = y2 - y1
    b = -(x2 - x1)
    c = (x2 - x1) * y1 - (y2 - y1) * x1

    distance = abs(a * x0 + b * y0 + c) / np.sqrt(a * a + b * b)
    return distance

def is_point_near_line_segment(point, line_start, line_end, threshold=5.0):
    """
    检查点是否在线段附近
    """
    distance = point_to_line_distance(point, line_start, line_end)
    return distance <= threshold

def handle_single_isolated_corner(vertices, corner_marks, isolated_index, noise_points):
    """
    处理单个孤立拐点的情况
    使用预存储的噪声点，通过几何计算生成有效边
    """
    print(f"      处理单个孤立拐点 (索引 {isolated_index})")

    isolated_point = vertices[isolated_index]

    # 找到前后相邻的两个无效拐点
    prev_index = (isolated_index - 1) % len(vertices)
    next_index = (isolated_index + 1) % len(vertices)

    if corner_marks[prev_index] != 0 or corner_marks[next_index] != 0:
        print(f"        警告：孤立拐点的前后拐点不都是无效拐点")
        return []

    prev_point = vertices[prev_index]
    next_point = vertices[next_index]

    # 从预存储的噪声点中找最近的噪声点
    inner_point = find_closest_noise_point_from_list(isolated_point, noise_points)

    if inner_point is None:
        print(f"        警告：未找到预存储的噪声点")
        return []

    print(f"        孤立拐点: {isolated_point}")
    print(f"        前拐点: {prev_point}")
    print(f"        后拐点: {next_point}")
    print(f"        内部点: {inner_point}")

    # 计算两条垂直距离
    distance1 = point_to_line_distance(inner_point, isolated_point, prev_point)
    distance2 = point_to_line_distance(inner_point, isolated_point, next_point)

    print(f"        垂直距离1 (内部点到孤立-前拐点线): {distance1:.2f}")
    print(f"        垂直距离2 (内部点到孤立-后拐点线): {distance2:.2f}")

    # 返回两个垂直距离作为新的有效边
    return [distance1, distance2]

def handle_two_isolated_corners(vertices, corner_marks, isolated_indices):
    """
    处理两个孤立拐点的情况
    """
    print(f"      处理两个孤立拐点 (索引 {isolated_indices})")

    point1 = vertices[isolated_indices[0]]
    point2 = vertices[isolated_indices[1]]

    # 计算两个孤立拐点之间的距离
    line_length = calculate_distance(point1, point2)
    print(f"        两个孤立拐点间距离: {line_length:.2f}")

    # 检查线段上是否有无效拐点
    invalid_indices = [i for i, mark in enumerate(corner_marks) if mark == 0]
    has_invalid_on_line = False

    for invalid_idx in invalid_indices:
        invalid_point = vertices[invalid_idx]
        if is_point_near_line_segment(invalid_point, point1, point2, threshold=5.0):
            print(f"        发现无效拐点 {invalid_idx} 在线段附近")
            has_invalid_on_line = True
            break

    if has_invalid_on_line:
        print(f"        线段上有无效拐点，使用原始长度: {line_length:.2f}")
        return [line_length]
    else:
        adjusted_length = line_length / np.sqrt(2)
        print(f"        线段上无无效拐点，使用调整后长度: {adjusted_length:.2f} (原长度/{np.sqrt(2):.3f})")
        return [adjusted_length]

def handle_three_isolated_corners(vertices, isolated_indices):
    """
    处理三个孤立拐点的情况
    """
    print(f"      处理三个孤立拐点 (索引 {isolated_indices})")

    # 按照在多边形中的位置顺序确定中间的孤立拐点
    sorted_indices = sorted(isolated_indices)

    # 选择第一个和第三个孤立拐点（跳过中间的）
    point1 = vertices[sorted_indices[0]]
    point3 = vertices[sorted_indices[2]]

    line_length = calculate_distance(point1, point3)

    print(f"        连接拐点 {sorted_indices[0]} 和 {sorted_indices[2]}，忽略中间拐点 {sorted_indices[1]}")
    print(f"        线段长度: {line_length:.2f}")

    return [line_length]

def handle_isolated_corners(vertices, corner_marks, isolated_indices, noise_points):
    """
    处理孤立拐点的特殊情况
    """
    additional_edges = []
    isolated_count = len(isolated_indices)

    print(f"    开始处理 {isolated_count} 个孤立拐点: {isolated_indices}")

    if isolated_count == 1:
        # 情况1：1个孤立拐点
        additional_edges.extend(handle_single_isolated_corner(vertices, corner_marks, isolated_indices[0],
                                                            noise_points))

    elif isolated_count == 2:
        # 情况2：2个孤立拐点
        additional_edges.extend(handle_two_isolated_corners(vertices, corner_marks, isolated_indices))

    elif isolated_count == 3:
        # 情况3：3个孤立拐点
        additional_edges.extend(handle_three_isolated_corners(vertices, isolated_indices))

    else:
        print(f"    不支持的孤立拐点数量: {isolated_count}")

    return additional_edges

############计算整边长度（包含孤立拐点处理）##################
def get_valid_edge_lengths(edge_arrays, jd_dict=None, flipped_binary=None):
    """
    计算所有标志位为1的边的边长，并处理孤立拐点，返回一行的数组
    参数：
    - edge_arrays: 存储Edge1、Edge2等边数组的字典
    - jd_dict: 拐点数组字典，用于孤立拐点处理
    - flipped_binary: 翻转后的二值图像，用于提取噪声点
    返回：包含所有有效边（标志位1）长度的1维数组
    """
    if not edge_arrays:
        return np.array([])

    valid_lengths = []

    # 遍历所有边数组，计算标志位为1的边长
    for edge_name, edge_array in edge_arrays.items():
        # 提取标志位为1的边的索引
        valid_indices = np.where(edge_array[4] == 1)[0]

        # 计算这些边的长度
        for i in valid_indices:
            # 当前点坐标
            x1, y1 = edge_array[0, i], edge_array[1, i]
            # 下一点坐标
            x2, y2 = edge_array[2, i], edge_array[3, i]
            # 计算欧氏距离
            length = calculate_distance((x1, y1), (x2, y2))
            valid_lengths.append(length)

    # 处理孤立拐点（如果提供了相关参数）
    if jd_dict is not None and flipped_binary is not None:
        print("  开始处理孤立拐点...")

        for jd_name, jd_array in jd_dict.items():
            # 提取顶点和标记
            n = jd_array.shape[1]
            vertices = [(jd_array[0, i], jd_array[1, i]) for i in range(n)]
            corner_marks = [int(jd_array[2, i]) for i in range(n)]

            # 找到孤立拐点（标记为2）
            isolated_indices = [i for i, mark in enumerate(corner_marks) if mark == 2]

            if isolated_indices:
                print(f"  {jd_name} 发现 {len(isolated_indices)} 个孤立拐点: {isolated_indices}")

                # 提取对应轮廓的噪声点（这里简化处理，实际应该从轮廓中提取）
                # 为了演示，我们创建一个简单的噪声点列表
                noise_points = []
                if flipped_binary is not None:
                    # 在拐点周围搜索噪声点
                    for vertex in vertices:
                        x, y = int(vertex[0]), int(vertex[1])
                        for dy in range(-10, 11):
                            for dx in range(-10, 11):
                                nx, ny = x + dx, y + dy
                                if (0 <= nx < flipped_binary.shape[1] and
                                    0 <= ny < flipped_binary.shape[0] and
                                    flipped_binary[ny, nx] > 0):
                                    noise_points.append((nx, ny))

                # 处理孤立拐点
                additional_edges = handle_isolated_corners(vertices, corner_marks, isolated_indices, noise_points)
                if additional_edges:
                    valid_lengths.extend(additional_edges)
                    print(f"  {jd_name} 孤立拐点处理后新增 {len(additional_edges)} 条有效边")

    # 转换为1行的数组返回
    return np.array(valid_lengths).reshape(1, -1) if valid_lengths else np.array([])

############拼接不完整边#################
def filter_and_concat_edges(edge_arrays):
    """
    保留所有标志位不为1的边，删除标志位为1的边，然后拼接所有轮廓的边数组
    参数：edge_arrays - 存储Edge1、Edge2等边数组的字典
    返回：拼接后的5*n数组（n为所有保留边的总数）
    """
    if not edge_arrays:
        return np.array([])
    
    filtered_edges = []
    
    # 遍历所有边数组，筛选标志位不为1的边
    for edge_array in edge_arrays.values():
        # 提取标志位不为1的列索引
        keep_indices = np.where(edge_array[4] != 1)[0]
        if len(keep_indices) > 0:
            # 保留这些列
            filtered = edge_array[:, keep_indices]
            filtered_edges.append(filtered)
    
    # 如果没有保留的边，返回空数组
    if not filtered_edges:
        return np.array([])
    
    # 按列拼接所有保留的边数组（5*n）
    concatenated = np.hstack(filtered_edges)
    return concatenated

###########寻找平行的不完整边#############
def detect_parallel_edges(concatenated_edges, angle_tolerance):
    """
    从拼接后的边数组中识别相互平行的边组合
    参数：
        concatenated_edges: 拼接后的5*n边数组（5行，n列）
        angle_tolerance: 角度容忍度（度），小于该值认为角度相同（平行）
    返回：
        8*m数组，m为平行边组合数，前4行为第一条边两点坐标，后4行为第二条边两点坐标
    """
    if concatenated_edges.size == 0:
        return np.array([])
    
    # 提取所有边的坐标（忽略标志位）
    num_edges = concatenated_edges.shape[1]
    edges = []  # 存储每条边的两点坐标 (x1,y1,x2,y2)
    
    for i in range(num_edges):
        x1, y1 = concatenated_edges[0, i], concatenated_edges[1, i]
        x2, y2 = concatenated_edges[2, i], concatenated_edges[3, i]
        edges.append((x1, y1, x2, y2))
    
    # 计算每条边的"无向角度"（将方向相反的角度视为相同）
    # 处理方式：将角度归一化到 [0, π) 范围（180度内）
    edge_angles = []
    for (x1, y1, x2, y2) in edges:
        dx = x2 - x1
        dy = y2 - y1
        
        # 计算原始角度（弧度，范围(-π, π]）
        if dx == 0 and dy == 0:
            angle = 0.0  # 无效边
        else:
            angle = np.arctan2(dy, dx)
        
        # 归一化到 [0, π)：将负角度转为正角度，超过π的减去π
        # 例如：-30°（-π/6）→ 150°（5π/6）；210°（7π/6）→ 30°（π/6）
        angle = angle % np.pi  # 取模运算实现归一化
        edge_angles.append(angle)
    
    # 寻找所有平行边组合（i < j 避免重复组合）
    parallel_pairs = []
    for i in range(num_edges):
        for j in range(i + 1, num_edges):
            # 计算角度差（已归一化到[0,π)，直接计算绝对值）
            angle_diff = np.abs(edge_angles[i] - edge_angles[j]) * (180 / np.pi)  # 转为度
            
            # 角度差小于容忍度，认为平行（包括方向相反的情况）
            if angle_diff <= angle_tolerance:
                # 第一条边的两点坐标（4行）
                edge_i = [
                    edges[i][0],  # x1
                    edges[i][1],  # y1
                    edges[i][2],  # x2
                    edges[i][3]   # y2
                ]
                # 第二条边的两点坐标（4行）
                edge_j = [
                    edges[j][0],  # x1
                    edges[j][1],  # y1
                    edges[j][2],  # x2
                    edges[j][3]   # y2
                ]
                parallel_pairs.append(edge_i + edge_j)
    
    # 转换为8*m数组（8行，m列）
    if not parallel_pairs:
        return np.array([])
    
    # 转置使每行对应要求的坐标行（8行，m列）
    result = np.array(parallel_pairs).T
    return result

###########判别并计算不平行边的距离################
def calculate_parallel_edge_distances(parallel_edges, binary_img, black_tolerance):
    """
    处理平行边数组，判断其组成的四边形内部是否全黑，计算符合条件的边对距离
    参数：
        parallel_edges: 8*m的平行边数组（前4行为第一条边，后4行为第二条边）
        binary_img: 二值化图像（用于判断四边形内部是否全黑）
        black_tolerance: 黑色像素比例阈值（默认0.95，即≥95%视为全黑）
    返回：
        1*k的数组，k为符合条件的平行边对数量，元素为对应的距离
    """
    if parallel_edges.size == 0:
        return np.array([])
    
    distances = []
    m = parallel_edges.shape[1]  # 平行边组合数
    
    # 遍历每组平行边
    for i in range(m):
        # 提取两条边的四个端点坐标
        # 第一条边：(x1,y1) -> (x2,y2)
        x1, y1 = parallel_edges[0, i], parallel_edges[1, i]
        x2, y2 = parallel_edges[2, i], parallel_edges[3, i]
        # 第二条边：(x3,y3) -> (x4,y4)
        x3, y3 = parallel_edges[4, i], parallel_edges[5, i]
        x4, y4 = parallel_edges[6, i], parallel_edges[7, i]
        
        # 构建四边形的四个顶点（按顺序排列）
        # 这里假设两条平行边为对边，连接端点形成四边形
        pts = np.array([
            [x1, y1], [x2, y2],
            [x4, y4], [x3, y3]
        ], dtype=np.int32)
        
        # 获取四边形的边界框（用于裁剪图像区域）
        min_x = np.min(pts[:, 0])
        max_x = np.max(pts[:, 0])
        min_y = np.min(pts[:, 1])
        max_y = np.max(pts[:, 1])
        
        # 确保边界在图像范围内
        h, w = binary_img.shape[:2]
        min_x = max(0, min_x)
        max_x = min(w - 1, max_x)
        min_y = max(0, min_y)
        max_y = min(h - 1, max_y)
        
        # 如果边界框无效（面积为0），跳过
        if min_x >= max_x or min_y >= max_y:
            continue
        
        # 创建掩膜，标记四边形内部区域
        mask = np.zeros((h, w), dtype=np.uint8)
        cv2.fillPoly(mask, [pts], 255)  # 四边形内部填充白色
        
        # 裁剪出四边形区域
        roi_mask = mask[min_y:max_y+1, min_x:max_x+1]
        roi_img = binary_img[min_y:max_y+1, min_x:max_x+1]
        
        # 计算四边形内部的黑色像素比例
        total_pixels = np.sum(roi_mask == 255)  # 四边形内部总像素
        if total_pixels == 0:
            continue
        
        # 统计黑色像素（目标，0值）数量
        black_pixels = np.sum((roi_img == 0) & (roi_mask == 255))
        black_ratio = black_pixels / total_pixels
        
        # 如果黑色比例达到阈值，计算两条平行边的距离
        if black_ratio >= black_tolerance:
            # 计算两条平行线之间的距离（使用点到直线的距离公式）
            # 直线1：ax + by + c1 = 0
            a = y2 - y1
            b = x1 - x2
            c1 = x2*y1 - x1*y2
            
            # 取第二条边上的一点计算到直线1的距离
            # 距离公式：|a*x3 + b*y3 + c1| / sqrt(a² + b²)
            numerator = np.abs(a * x3 + b * y3 + c1)
            denominator = np.sqrt(a**2 + b**2)
            
            if denominator > 1e-6:  # 避免除以0
                distance = numerator / denominator
                distances.append(distance)
    
    # 转换为1行的数组返回
    return np.array(distances).reshape(1, -1)


#############################################################################
# 检查设备初始化状态
if cam is None:
    print("错误：摄像头未正确初始化，程序退出")
    exit(1)

if disp is None:
    print("警告：显示器未正确初始化，将只输出调试信息")

print("开始主循环...")

# 主循环：持续处理图像，直到需要退出程序
while not app.need_exit():
    # 从摄像头读取一帧图像
    img = cam.read()

    # 调试：检查摄像头读取是否成功
    if img is None:
        print("错误：摄像头读取失败，img为None")
        continue

    print(f"调试：摄像头读取成功，图像类型: {type(img)}")

    # 将MaixPy的image对象转换为OpenCV的numpy数组（灰度图，无需BGR转换）
    try:
        img = image.image2cv(img, ensure_bgr=False, copy=False)
        print(f"调试：图像转换成功，OpenCV图像形状: {img.shape}")
    except Exception as e:
        print(f"错误：图像转换失败: {e}")
        continue

    # 检查图像是否为空
    if img.size == 0:
        print("错误：转换后的图像为空")
        continue

    # 从原始图像中截取ROI区域（只处理感兴趣的部分）
    roi = img[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
    print(f"调试：ROI截取成功，ROI形状: {roi.shape}")
    
    # 对ROI区域进行二值化处理（阈值40，大于40为白(255)，小于等于40为黑(0)）
    ret, binary = cv2.threshold(roi, 60, 255, cv2.THRESH_BINARY)
    
    # 对二值化图像进行翻转（用于检测高亮区域，黑变白、白变黑）
    flipped_binary = cv2.bitwise_not(binary)
    
    # 在翻转后的图像上寻找轮廓（只检测外轮廓，轮廓点用简化方式存储）
    contours, _ = cv2.findContours(flipped_binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 将二值化图像转换为BGR格式（以便绘制彩色标记，如红色角点、绿色轮廓）
    binary_bgr = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
    
    # 在BGR图像上绘制ROI区域的边框（红色，线宽2）
    cv2.rectangle(binary_bgr, (0, 0), (roi_w-1, roi_h-1), (0, 0, 255), 2)
    
    # 初始化存储所有角点的列表
    all_corners = []
    # 初始化存储每个轮廓角点的列表（每个元素是一个轮廓的角点数组）
    contour_corners_list = []  # 存储每个轮廓的角点数组
    # 轮廓全局编号计数器（从0开始，每处理一个轮廓加1）
    contour_id = 0
    # 角点全局编号计数器（从0开始，每检测一个角点加1）
    global_corner_id = 0
    
    # 遍历每个检测到的轮廓
    for contour in contours:
        # 轮廓编号递增（使ID从1开始）
        contour_id += 1
        
        # 计算轮廓的周长（闭合轮廓）
        perimeter = cv2.arcLength(contour, True)
        
        # 设置多边形逼近的精度（epsilon为周长的1%，值越小逼近越精确）
        epsilon = 0.01 * perimeter
        
        # 对轮廓进行多边形逼近，得到角点（approx为角点坐标的数组）
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 在BGR图像上绘制轮廓（绿色，线宽2）
        cv2.drawContours(binary_bgr, [contour], -1, (0, 255, 0), 2)
        
        # 计算轮廓的矩（用于求轮廓中心）
        M = cv2.moments(contour)
        # 若轮廓面积不为0（避免除0错误），计算中心坐标并绘制轮廓编号
        if M["m00"] != 0:
            cX = int(M["m10"] / M["m00"])  # 轮廓中心x坐标
            cY = int(M["m01"] / M["m00"])  # 轮廓中心y坐标
            # 在轮廓中心附近绘制轮廓编号（青色，字体大小0.5，线宽2）
            cv2.putText(binary_bgr, f"{contour_id}", (cX - 30, cY - 20),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
        
        # 初始化当前轮廓的角点列表
        contour_corners = []
        # 角点局部编号计数器（每个轮廓内从0开始，递增后从1开始）
        local_corner_id = 0
        
        # 遍历逼近得到的每个角点
        for point in approx:
            # 全局角点编号递增
            global_corner_id += 1
            # 局部角点编号递增（当前轮廓内从1开始）
            local_corner_id += 1
            
            # 提取角点的x、y坐标（point是二维数组，取第一个元素）
            x, y = point[0]
            
            # 将角点信息添加到当前轮廓的角点列表（包含坐标、轮廓ID、全局/局部编号）
            contour_corners.append((x, y, contour_id, global_corner_id, local_corner_id))
            # 将角点信息添加到所有角点的列表
            all_corners.append((x, y, contour_id, global_corner_id, local_corner_id))
            
            # 在BGR图像上绘制角点（红色圆点，半径5，填充）
            cv2.circle(binary_bgr, (x, y), 5, (0, 0, 255), -1)
            # 在角点附近绘制局部编号（蓝色，字体大小0.5，线宽2）
            cv2.putText(binary_bgr, f"{local_corner_id}", (x+8, y-8), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        # 将当前轮廓的角点列表添加到总列表中
        contour_corners_list.append(contour_corners)  # 收集当前轮廓的角点数组
    
    # 新增：调用拐点数组处理函数（使用初始代码的拐点分析方法）
    jd_dict1=process_contour_corners(contour_corners_list,flipped_binary)
    edge_arrays1=convert_jd_to_edge_array(jd_dict1)
    SX1=get_valid_edge_lengths(edge_arrays1, jd_dict1, flipped_binary)
    print("完整边长度（包含孤立拐点处理）为:\n",SX1)
    edge_arrays2=filter_and_concat_edges(edge_arrays1)
    print("不完整边数组为:\n",edge_arrays2)
    paredge_arrays=detect_parallel_edges(edge_arrays2, 5.0)
    print("平行的不完整边数组为:\n",paredge_arrays)
    SX2=calculate_parallel_edge_distances(paredge_arrays, binary, 0.9)
    print("不完整边算出长度为:\n",SX2)
    # 若检测到角点，打印所有角点的信息
    if all_corners:
        print("\n检测到的角点:")
        for x, y, c_id, g_id, l_id in all_corners:
            # 将ROI内的坐标转换为原始图像中的坐标（加上ROI的偏移量）
            orig_x = x + roi_x
            orig_y = y + roi_y
            # 打印角点的全局/局部编号、所属轮廓及坐标信息
            print(f"角点 G{g_id}/L{l_id} (轮廓 {c_id}): ROI内坐标 ({x}, {y})，原始图像坐标 ({orig_x}, {orig_y})")
    
    # 确保图像尺寸是2的倍数（避免显示问题）
    binary_bgr = ensure_multiple_of_two(binary_bgr)
    print(f"调试：准备显示的图像形状: {binary_bgr.shape}")

    # 将OpenCV的numpy数组转换为MaixPy的image对象（BGR格式），用于显示
    try:
        img_show = image.cv2image(binary_bgr, bgr=True, copy=False)
        print(f"调试：图像转换为MaixPy格式成功，类型: {type(img_show)}")

        # 在显示器上显示处理后的图像
        if disp is not None:
            disp.show(img_show)
            print("调试：图像显示命令已执行")
        else:
            print("警告：显示器不可用，跳过显示")

    except Exception as e:
        print(f"错误：图像显示失败: {e}")
        print(f"binary_bgr类型: {type(binary_bgr)}, 形状: {binary_bgr.shape}, 数据类型: {binary_bgr.dtype}")

        # 尝试保存图像到文件进行调试
        try:
            cv2.imwrite("debug_image.jpg", binary_bgr)
            print("调试：图像已保存为debug_image.jpg，请检查文件")
        except Exception as save_e:
            print(f"保存图像也失败: {save_e}")